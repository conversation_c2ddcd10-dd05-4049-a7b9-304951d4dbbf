import <PERSON> from 'papa<PERSON><PERSON>';
import fs from 'fs';
import path from 'path';
import { executeQuery, executeTransaction } from '../db';

interface TransactionRecord {
  id?: number;
  reference_no?: string;
  transaction_id: string;
  transaction_out_id?: string;
  transaction_card_no?: string;
  transaction_merchant_id?: string;
  transaction_merchant_name?: string;
  transaction_merchant_vat?: string;
  transaction_time: Date;
  transaction_amount: number;
  transaction_refund_id?: string;
  transaction_refund_out_id?: string;
  transaction_mch_id?: string;
  transaction_sub_mch_id?: string;
  transaction_trade_type?: string;
  transaction_trade_status?: string;
  transaction_bank_type?: string;
  transaction_fee_type?: string;
  transaction_coupon_amount?: number;
  transaction_file_name: string;
  transaction_file_name_backup?: string;
  transaction_channel_type?: string;
  create_by?: string;
  create_dt?: Date;
  update_by?: string;
  update_dt?: Date;
}

interface ProcessingResult {
  success: boolean;
  totalRows: number;
  processedRows: number;
  skippedRows: number;
  errorRows: number;
  errors: string[];
  transactions: TransactionRecord[];
}

interface DuplicateHandlingOptions {
  strategy: 'SKIP' | 'UPDATE' | 'LOG_ONLY' | 'MERGE' | 'INSERT_ALL';
  logDuplicates: boolean;
  updateFields?: string[];
}

interface DuplicateLogEntry {
  transaction_id: string;
  original_file: string;
  duplicate_file: string;
  original_amount: number;
  duplicate_amount: number;
  action_taken: string;
  detected_at: Date;
}

interface MerchantInfo {
  merchant_id: string;
  merchant_name: string;
  merchant_vat: string;
}

// Column mapping constants for better performance
interface ColumnMapping {
  TRANSACTION_TIME: number;
  OFFICIAL_ACCOUNT_ID: number;
  VENDOR_ID: number;
  SUB_VENDOR_ID: number;
  DEVICE_ID: number;
  WECHAT_ORDER_NUMBER: number;
  VENDOR_ORDER_NUMBER: number;
  USER_TAG: number;
  TRANSACTION_TYPE: number;
  TRANSACTION_STATUS: number;
  PAYMENT_BANK: number;
  CURRENCY_TYPE: number;
  TOTAL_AMOUNT: number;
  COUPON_AMOUNT: number;
  WECHAT_REFUND_NUMBER: number;
  VENDOR_REFUND_NUMBER: number;
  REFUND_AMOUNT: number;
}

// Optimized column mapping for WeChat CSV format
const WECHAT_COLUMNS: ColumnMapping = {
  TRANSACTION_TIME: 0,
  OFFICIAL_ACCOUNT_ID: 1,
  VENDOR_ID: 2,
  SUB_VENDOR_ID: 3,
  DEVICE_ID: 4,
  WECHAT_ORDER_NUMBER: 5,
  VENDOR_ORDER_NUMBER: 6,
  USER_TAG: 7,
  TRANSACTION_TYPE: 8,
  TRANSACTION_STATUS: 9,
  PAYMENT_BANK: 10,
  CURRENCY_TYPE: 11,
  TOTAL_AMOUNT: 12,
  COUPON_AMOUNT: 13,
  WECHAT_REFUND_NUMBER: 14,
  VENDOR_REFUND_NUMBER: 15,
  REFUND_AMOUNT: 16
};

export class TransactionProcessingService {
  // Cache for merchant lookups to avoid repeated database queries
  private merchantCache = new Map<string, MerchantInfo | null>();

  /**
   * Process CSV/Excel file and extract transaction data - OPTIMIZED VERSION
   */
  async processTransactionFile(
    filePath: string,
    fileName: string,
    backupPath: string,
    currentUser: string = 'SYSTEM'
  ): Promise<ProcessingResult> {
    const result: ProcessingResult = {
      success: false,
      totalRows: 0,
      processedRows: 0,
      skippedRows: 0,
      errorRows: 0,
      errors: [],
      transactions: []
    };

    try {
      console.log(`📄 OPTIMIZED: Processing transaction file: ${fileName}`);
      const startTime = Date.now();

      // Pre-load all merchants to cache for faster lookups
      await this.preloadMerchantCache();

      // Use optimized CSV parsing
      const rows = await this.optimizedCsvParsing(filePath);
      result.totalRows = rows.length;

      console.log(`� OPTIMIZED: Found ${rows.length} rows in ${Date.now() - startTime}ms`);

      // Find header row efficiently
      const headerRowIndex = this.findHeaderRowIndex(rows);

      // Filter data rows efficiently
      const dataRows = this.filterDataRows(rows, headerRowIndex, result);

      console.log(`📋 OPTIMIZED: Processing ${dataRows.length} data rows`);

      // Determine channel type once
      const channelType = this.determineChannelType(fileName);

      // Batch process transactions for better performance
      const batchSize = 500; // Smaller batches for memory efficiency
      const allParsedTransactions: TransactionRecord[] = [];

      for (let i = 0; i < dataRows.length; i += batchSize) {
        const batch = dataRows.slice(i, i + batchSize);
        console.log(`� OPTIMIZED: Processing batch ${Math.floor(i/batchSize) + 1}/${Math.ceil(dataRows.length/batchSize)} (${batch.length} rows)`);

        const batchTransactions = await this.processBatchOptimized(
          batch, fileName, backupPath, channelType, currentUser, i + headerRowIndex + 2
        );

        allParsedTransactions.push(...batchTransactions.transactions);
        result.errorRows += batchTransactions.errorCount;
        result.errors.push(...batchTransactions.errors);
      }

      // Validate refund transactions efficiently
      const validatedTransactions = this.validateRefundsInBatch(allParsedTransactions);

      result.transactions = validatedTransactions.valid;
      result.processedRows = validatedTransactions.valid.length;
      result.skippedRows += validatedTransactions.skipped.length;

      // Log skipped refunds
      validatedTransactions.skipped.forEach(txn => {
        console.log(`⚠️ OPTIMIZED: Refund transaction skipped (no matching success): ${txn.transaction_id}`);
      });

      result.success = result.errorRows === 0 || result.processedRows > 0;

      const totalTime = Date.now() - startTime;
      console.log(`✅ OPTIMIZED: Processing complete in ${totalTime}ms: ${result.processedRows} processed, ${result.errorRows} errors, ${result.skippedRows} skipped`);

      return result;

    } catch (error) {
      console.error('❌ OPTIMIZED: Error processing transaction file:', error);
      result.errors.push(`File processing error: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return result;
    }
  }

  /**
   * OPTIMIZED: Pre-load all merchants into cache for faster lookups
   */
  private async preloadMerchantCache(): Promise<void> {
    try {
      console.log('🔄 OPTIMIZED: Pre-loading merchant cache...');
      const startTime = Date.now();

      const result = await executeQuery(
        'SELECT merchant_ref, merchant_name, merchant_vat, merchant_id_wechat FROM merchant WHERE active = true'
      );

      for (const row of result.rows) {
        if (row.merchant_id_wechat) {
          this.merchantCache.set(row.merchant_id_wechat, {
            merchant_id: row.merchant_ref,
            merchant_name: row.merchant_name,
            merchant_vat: row.merchant_vat
          });
        }
      }

      console.log(`✅ OPTIMIZED: Loaded ${this.merchantCache.size} merchants in ${Date.now() - startTime}ms`);
    } catch (error) {
      console.error('❌ OPTIMIZED: Error preloading merchant cache:', error);
    }
  }

  /**
   * OPTIMIZED: Fast CSV parsing with minimal overhead
   */
  private async optimizedCsvParsing(filePath: string): Promise<string[][]> {
    const fileContent = fs.readFileSync(filePath, 'utf8');

    // Try Papa Parse first (fastest for well-formed CSV)
    try {
      const parseResult = Papa.parse(fileContent, {
        header: false,
        skipEmptyLines: true,
        delimiter: ',',
        quoteChar: '`',
        escapeChar: '`'
      });

      if (parseResult.errors.length === 0 && parseResult.data.length > 0) {
        const rows = parseResult.data as string[][];
        if (rows[0] && rows[0].length >= 10) {
          console.log('✅ OPTIMIZED: Papa Parse successful');
          return rows;
        }
      }
    } catch (error) {
      console.log('⚠️ OPTIMIZED: Papa Parse failed, trying manual parsing');
    }

    // Fallback to optimized manual parsing
    return this.manualCsvParsing(fileContent);
  }

  /**
   * OPTIMIZED: Manual CSV parsing for WeChat format
   */
  private manualCsvParsing(fileContent: string): string[][] {
    const lines = fileContent.split('\n');
    const rows: string[][] = [];

    for (const line of lines) {
      const trimmedLine = line.trim();
      if (!trimmedLine) continue;

      // Remove BOM if present
      const cleanLine = trimmedLine.replace(/^\uFEFF/, '');

      // Fast parsing for backtick-quoted CSV
      if (cleanLine.includes('`,`')) {
        // Split by comma-backtick pattern and clean up
        const fields = cleanLine.split('`,`').map((field, index, array) => {
          let cleaned = field;
          if (index === 0 && cleaned.startsWith('`')) {
            cleaned = cleaned.substring(1);
          }
          if (index === array.length - 1 && cleaned.endsWith('`')) {
            cleaned = cleaned.substring(0, cleaned.length - 1);
          }
          return cleaned.trim().replace(/\r$/, '');
        });

        if (fields.length > 5) {
          rows.push(fields);
        }
      } else {
        // Regular comma-separated (likely header)
        const fields = cleanLine.split(',').map(field => field.trim());
        if (fields.length > 5) {
          rows.push(fields);
        }
      }
    }

    console.log(`✅ OPTIMIZED: Manual parsing completed - ${rows.length} rows`);
    return rows;
  }

  /**
   * OPTIMIZED: Find header row index efficiently
   */
  private findHeaderRowIndex(rows: string[][]): number {
    for (let i = 0; i < Math.min(3, rows.length); i++) {
      const firstCol = rows[i]?.[0]?.replace(/[`\uFEFF]/g, '').trim();
      if (firstCol?.includes('Transaction time') || firstCol?.includes('Official account ID')) {
        console.log(`🔍 OPTIMIZED: Found header row at index ${i}`);
        return i;
      }
    }
    console.log('⚠️ OPTIMIZED: No clear header found, assuming first row is header');
    return 0;
  }

  /**
   * OPTIMIZED: Filter data rows efficiently
   */
  private filterDataRows(rows: string[][], headerRowIndex: number, result: ProcessingResult): string[][] {
    return rows.slice(headerRowIndex + 1).filter((row, index) => {
      const rowNumber = index + headerRowIndex + 2;

      // Skip empty rows
      if (!row || row.length === 0 || row[0]?.trim() === '') {
        result.skippedRows++;
        return false;
      }

      // Skip summary rows
      const firstCol = row[0]?.replace(/`/g, '').trim();
      if (firstCol?.includes('Total') || /^\d+\.?\d*$/.test(firstCol)) {
        result.skippedRows++;
        return false;
      }

      // Skip rows with too few columns
      if (row.length < 17) {
        result.skippedRows++;
        return false;
      }

      return true;
    });
  }

  /**
   * OPTIMIZED: Process batch of rows efficiently
   */
  private async processBatchOptimized(
    batch: string[][],
    fileName: string,
    backupPath: string,
    channelType: string,
    currentUser: string,
    startRowNumber: number
  ): Promise<{ transactions: TransactionRecord[]; errorCount: number; errors: string[] }> {
    const transactions: TransactionRecord[] = [];
    const errors: string[] = [];
    let errorCount = 0;

    for (let i = 0; i < batch.length; i++) {
      try {
        const row = batch[i];
        const transaction = await this.parseTransactionRowOptimized(
          row, fileName, backupPath, channelType, currentUser
        );

        if (transaction) {
          transactions.push(transaction);
        }
      } catch (error) {
        errorCount++;
        const rowNumber = startRowNumber + i;
        const errorMsg = `Row ${rowNumber}: ${error instanceof Error ? error.message : 'Unknown error'}`;
        errors.push(errorMsg);
      }
    }

    return { transactions, errorCount, errors };
  }

  /**
   * OPTIMIZED: Parse individual transaction row with cached merchant lookups
   */
  private async parseTransactionRowOptimized(
    row: string[],
    fileName: string,
    backupPath: string,
    channelType: string,
    currentUser: string
  ): Promise<TransactionRecord | null> {
    try {
      // OPTIMIZED: Use direct column access with constants
      const cols = WECHAT_COLUMNS;

      // OPTIMIZED: Fast field extraction with minimal string operations
      const cleanField = (index: number): string => row[index]?.replace(/`/g, '').trim() || '';

      // OPTIMIZED: Parse transaction time efficiently
      let transactionTimeStr = cleanField(cols.TRANSACTION_TIME);
      if (transactionTimeStr.includes(',')) {
        transactionTimeStr = transactionTimeStr.split(',')[0].trim();
      }

      // OPTIMIZED: Fast date parsing
      let transactionTime = new Date(transactionTimeStr);
      if (isNaN(transactionTime.getTime())) {
        // Try common format variations
        const isoFormat = transactionTimeStr.replace(/(\d{4}-\d{2}-\d{2})\s+(\d{2}:\d{2}:\d{2})/, '$1T$2');
        transactionTime = new Date(isoFormat);

        if (isNaN(transactionTime.getTime())) {
          throw new Error(`Invalid transaction time: ${transactionTimeStr}`);
        }
      }

      // OPTIMIZED: Get required fields
      const transactionId = cleanField(cols.WECHAT_ORDER_NUMBER);
      if (!transactionId) {
        throw new Error('Missing transaction ID');
      }

      const transactionOutId = cleanField(cols.VENDOR_ORDER_NUMBER);
      const subMchId = cleanField(cols.SUB_VENDOR_ID);

      // OPTIMIZED: Use cached merchant lookup
      const merchantInfo = this.merchantCache.get(subMchId) || null;

      // OPTIMIZED: Parse transaction status and amount efficiently
      const rawTradeStatus = cleanField(cols.TRANSACTION_STATUS);
      const tradeStatus = rawTradeStatus.toLowerCase();
      let transactionAmount = 0;

      if (tradeStatus === 'success') {
        const amountStr = cleanField(cols.TOTAL_AMOUNT);
        transactionAmount = parseFloat(amountStr) || 0;
      } else if (tradeStatus === 'refund') {
        const refundAmountStr = cleanField(cols.REFUND_AMOUNT);
        transactionAmount = -(parseFloat(refundAmountStr) || 0);
      }

      // OPTIMIZED: Parse coupon amount
      const couponAmountStr = cleanField(cols.COUPON_AMOUNT);
      const couponAmount = parseFloat(couponAmountStr) || 0;

      // OPTIMIZED: Create transaction record efficiently
      const transaction: TransactionRecord = {
        reference_no: transactionId,
        transaction_id: transactionId,
        transaction_out_id: transactionOutId,
        transaction_card_no: cleanField(cols.OFFICIAL_ACCOUNT_ID),
        transaction_merchant_id: merchantInfo?.merchant_id,
        transaction_merchant_name: merchantInfo?.merchant_name,
        transaction_merchant_vat: merchantInfo?.merchant_vat,
        transaction_time: transactionTime,
        transaction_amount: transactionAmount,
        transaction_refund_id: cleanField(cols.WECHAT_REFUND_NUMBER),
        transaction_refund_out_id: cleanField(cols.VENDOR_REFUND_NUMBER),
        transaction_mch_id: cleanField(cols.VENDOR_ID),
        transaction_sub_mch_id: subMchId,
        transaction_trade_type: cleanField(cols.TRANSACTION_TYPE),
        transaction_trade_status: tradeStatus,
        transaction_bank_type: cleanField(cols.PAYMENT_BANK),
        transaction_fee_type: cleanField(cols.CURRENCY_TYPE),
        transaction_coupon_amount: couponAmount,
        transaction_file_name: fileName,
        transaction_file_name_backup: backupPath,
        transaction_channel_type: channelType,
        create_by: currentUser,
        update_by: currentUser
      };

      return transaction;

    } catch (error) {
      throw error;
    }
  }

  /**
   * OPTIMIZED: Validate refunds in batch efficiently
   */
  private validateRefundsInBatch(transactions: TransactionRecord[]): {
    valid: TransactionRecord[];
    skipped: TransactionRecord[];
  } {
    const valid: TransactionRecord[] = [];
    const skipped: TransactionRecord[] = [];

    // Create a Set of success transaction IDs for O(1) lookup
    const successTransactionIds = new Set(
      transactions
        .filter(t => t.transaction_trade_status === 'success')
        .map(t => t.transaction_id)
    );

    for (const transaction of transactions) {
      if (transaction.transaction_trade_status === 'refund') {
        // Check if there's a matching success transaction
        if (successTransactionIds.has(transaction.transaction_id)) {
          valid.push(transaction);
        } else {
          skipped.push(transaction);
        }
      } else {
        // Non-refund transactions are always valid
        valid.push(transaction);
      }
    }

    return { valid, skipped };
  }



  /**
   * Determine channel type from filename
   */
  private determineChannelType(fileName: string): string {
    const lowerFileName = fileName.toLowerCase();
    
    if (lowerFileName.includes('wechat') || lowerFileName.includes('wx')) {
      return 'WeChat';
    } else if (lowerFileName.includes('uni') || lowerFileName.includes('union')) {
      return 'UNIPAY';
    } else if (lowerFileName.includes('alipay')) {
      return 'Alipay';
    }
    
    // Default based on file structure - if it has WeChat-style columns, assume WeChat
    return 'WeChat';
  }

  /**
   * OPTIMIZED: Save transactions to database using high-performance bulk insert
   */
  async saveTransactions(
    transactions: TransactionRecord[],
    _options?: DuplicateHandlingOptions
  ): Promise<{
    success: boolean;
    savedCount: number;
    duplicateCount: number;
    updatedCount: number;
    errorCount: number;
    errors: string[];
    duplicateDetails: DuplicateLogEntry[];
  }> {

    const result = {
      success: false,
      savedCount: 0,
      duplicateCount: 0,
      updatedCount: 0,
      errorCount: 0,
      errors: [] as string[],
      duplicateDetails: [] as DuplicateLogEntry[]
    };

    try {
      console.log(`💾 OPTIMIZED BULK INSERT: Saving ${transactions.length} transactions`);
      const startTime = Date.now();

      if (transactions.length === 0) {
        result.success = true;
        console.log('✅ OPTIMIZED: No transactions to save');
        return result;
      }

      // OPTIMIZED: Use larger batch sizes for better performance
      const batchSize = 2000; // Increased from 1000
      const batches = [];

      for (let i = 0; i < transactions.length; i += batchSize) {
        batches.push(transactions.slice(i, i + batchSize));
      }

      console.log(`📦 OPTIMIZED: Processing ${batches.length} batches of max ${batchSize} records each`);

      // OPTIMIZED: Process batches with better error handling
      for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
        const batch = batches[batchIndex];
        console.log(`🔄 OPTIMIZED: Processing batch ${batchIndex + 1}/${batches.length} with ${batch.length} records`);

        try {
          // OPTIMIZED: Pre-allocate arrays for better performance
          const values: string[] = new Array(batch.length);
          const params: any[] = new Array(batch.length * 23);
          let paramIndex = 1;

          // OPTIMIZED: Build query with minimal string operations
          for (let i = 0; i < batch.length; i++) {
            const transaction = batch[i];
            const baseIndex = i * 23;

            values[i] = `($${paramIndex}, $${paramIndex + 1}, $${paramIndex + 2}, $${paramIndex + 3}, $${paramIndex + 4}, $${paramIndex + 5}, $${paramIndex + 6}, $${paramIndex + 7}, $${paramIndex + 8}, $${paramIndex + 9}, $${paramIndex + 10}, $${paramIndex + 11}, $${paramIndex + 12}, $${paramIndex + 13}, $${paramIndex + 14}, $${paramIndex + 15}, $${paramIndex + 16}, $${paramIndex + 17}, $${paramIndex + 18}, $${paramIndex + 19}, $${paramIndex + 20}, $${paramIndex + 21}, $${paramIndex + 22}, NOW(), NOW())`;

            // OPTIMIZED: Direct array assignment instead of push
            params[baseIndex] = transaction.reference_no;
            params[baseIndex + 1] = transaction.transaction_id;
            params[baseIndex + 2] = transaction.transaction_out_id;
            params[baseIndex + 3] = transaction.transaction_card_no;
            params[baseIndex + 4] = transaction.transaction_merchant_id;
            params[baseIndex + 5] = transaction.transaction_merchant_name;
            params[baseIndex + 6] = transaction.transaction_merchant_vat;
            params[baseIndex + 7] = transaction.transaction_time;
            params[baseIndex + 8] = transaction.transaction_amount;
            params[baseIndex + 9] = transaction.transaction_refund_id;
            params[baseIndex + 10] = transaction.transaction_refund_out_id;
            params[baseIndex + 11] = transaction.transaction_mch_id;
            params[baseIndex + 12] = transaction.transaction_sub_mch_id;
            params[baseIndex + 13] = transaction.transaction_trade_type;
            params[baseIndex + 14] = transaction.transaction_trade_status;
            params[baseIndex + 15] = transaction.transaction_bank_type;
            params[baseIndex + 16] = transaction.transaction_fee_type;
            params[baseIndex + 17] = transaction.transaction_coupon_amount;
            params[baseIndex + 18] = transaction.transaction_file_name;
            params[baseIndex + 19] = transaction.transaction_file_name_backup;
            params[baseIndex + 20] = transaction.transaction_channel_type;
            params[baseIndex + 21] = transaction.create_by;
            params[baseIndex + 22] = transaction.update_by;

            paramIndex += 23;
          }

          // OPTIMIZED: Single bulk insert query
          const bulkInsertQuery = `
            INSERT INTO transaction_e_pos (
              reference_no, transaction_id, transaction_out_id, transaction_card_no,
              transaction_merchant_id, transaction_merchant_name, transaction_merchant_vat,
              transaction_time, transaction_amount, transaction_refund_id, transaction_refund_out_id,
              transaction_mch_id, transaction_sub_mch_id, transaction_trade_type, transaction_trade_status,
              transaction_bank_type, transaction_fee_type, transaction_coupon_amount,
              transaction_file_name, transaction_file_name_backup, transaction_channel_type,
              create_by, update_by, create_dt, update_dt
            ) VALUES ${values.join(', ')}
          `;

          const insertResult = await executeQuery(bulkInsertQuery, params);
          const insertedCount = insertResult.rowCount || 0;

          result.savedCount += insertedCount;

          console.log(`✅ OPTIMIZED: Batch ${batchIndex + 1} complete: ${insertedCount} records inserted`);

        } catch (error) {
          console.error(`❌ OPTIMIZED: Error processing batch ${batchIndex + 1}:`, error);

          result.errorCount += batch.length;
          const errorMsg = `Batch ${batchIndex + 1} failed: ${error instanceof Error ? error.message : 'Unknown error'}`;
          result.errors.push(errorMsg);
        }
      }

      // Success if we inserted any records
      result.success = result.savedCount > 0;

      const totalTime = Date.now() - startTime;
      console.log(`✅ OPTIMIZED BULK INSERT complete in ${totalTime}ms: ${result.savedCount} records inserted, ${result.errorCount} errors`);
      console.log(`📊 OPTIMIZED: Performance: ${Math.round(result.savedCount / (totalTime / 1000))} records/second`);

      return result;

    } catch (error) {
      console.error('❌ OPTIMIZED: Error in BULK INSERT operation:', error);
      result.errors.push(`Database error: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return result;
    }
  }


}
